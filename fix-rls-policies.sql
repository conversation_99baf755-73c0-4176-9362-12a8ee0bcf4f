-- =====================================================
-- FIX RLS POLICIES TO PREVENT INFINITE RECURSION
-- Run this script in Supabase SQL Editor to fix the issue
-- =====================================================

-- Drop all existing policies that cause recursion
DROP POLICY IF EXISTS "Users can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
DROP POLICY IF EXISTS "Admins can manage all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Service role can manage all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Authenticated users can insert own profile" ON user_profiles;

DROP POLICY IF EXISTS "Anyone can view published articles" ON articles;
DROP POLICY IF EXISTS "Authors can view own articles" ON articles;
DROP POLICY IF EXISTS "Writers can create articles" ON articles;
DROP POLICY IF EXISTS "Authors can update own articles" ON articles;
DROP POLICY IF EXISTS "Editors can manage all articles" ON articles;
DROP POLICY IF EXISTS "Authenticated users can create articles" ON articles;
DROP POLICY IF EXISTS "Service role can manage all articles" ON articles;

DROP POLICY IF EXISTS "Anyone can view approved comments" ON comments;
DROP POLICY IF EXISTS "Users can create comments" ON comments;
DROP POLICY IF EXISTS "Users can update own comments" ON comments;
DROP POLICY IF EXISTS "Moderators can manage comments" ON comments;
DROP POLICY IF EXISTS "Service role can manage comments" ON comments;

DROP POLICY IF EXISTS "Users can manage own bookmarks" ON bookmarks;

DROP POLICY IF EXISTS "Anyone can subscribe to newsletter" ON newsletter_subscribers;
DROP POLICY IF EXISTS "Admins can manage newsletter" ON newsletter_subscribers;
DROP POLICY IF EXISTS "Service role can manage newsletter" ON newsletter_subscribers;

DROP POLICY IF EXISTS "Anyone can view active ads" ON advertisements;
DROP POLICY IF EXISTS "Admins can manage ads" ON advertisements;
DROP POLICY IF EXISTS "Service role can manage ads" ON advertisements;

DROP POLICY IF EXISTS "Anyone can view public settings" ON website_settings;
DROP POLICY IF EXISTS "Admins can manage settings" ON website_settings;
DROP POLICY IF EXISTS "Service role can manage settings" ON website_settings;

-- Create new policies without recursion
-- User profiles policies (fixed to avoid infinite recursion)
CREATE POLICY "Users can view all profiles" ON user_profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Service role can manage all profiles" ON user_profiles FOR ALL USING (
    auth.role() = 'service_role'
);
CREATE POLICY "Authenticated users can insert own profile" ON user_profiles FOR INSERT WITH CHECK (
    auth.uid() = id
);

-- Articles policies (simplified to avoid recursion)
CREATE POLICY "Anyone can view published articles" ON articles FOR SELECT USING (is_published = true);
CREATE POLICY "Authors can view own articles" ON articles FOR SELECT USING (auth.uid() = author_id);
CREATE POLICY "Authenticated users can create articles" ON articles FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL
);
CREATE POLICY "Authors can update own articles" ON articles FOR UPDATE USING (auth.uid() = author_id);
CREATE POLICY "Service role can manage all articles" ON articles FOR ALL USING (
    auth.role() = 'service_role'
);

-- Comments policies (simplified to avoid recursion)
CREATE POLICY "Anyone can view approved comments" ON comments FOR SELECT USING (is_approved = true);
CREATE POLICY "Users can create comments" ON comments FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own comments" ON comments FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Service role can manage comments" ON comments FOR ALL USING (
    auth.role() = 'service_role'
);

-- Bookmarks policies
CREATE POLICY "Users can manage own bookmarks" ON bookmarks FOR ALL USING (auth.uid() = user_id);

-- Newsletter policies (simplified to avoid recursion)
CREATE POLICY "Anyone can subscribe to newsletter" ON newsletter_subscribers FOR INSERT WITH CHECK (true);
CREATE POLICY "Service role can manage newsletter" ON newsletter_subscribers FOR ALL USING (
    auth.role() = 'service_role'
);

-- Advertisements policies (simplified to avoid recursion)
CREATE POLICY "Anyone can view active ads" ON advertisements FOR SELECT USING (is_active = true);
CREATE POLICY "Service role can manage ads" ON advertisements FOR ALL USING (
    auth.role() = 'service_role'
);

-- Website settings policies (simplified to avoid recursion)
CREATE POLICY "Anyone can view public settings" ON website_settings FOR SELECT USING (is_public = true);
CREATE POLICY "Service role can manage settings" ON website_settings FOR ALL USING (
    auth.role() = 'service_role'
);

-- Verification
SELECT 'RLS POLICIES FIXED!' as status;
SELECT 'You can now test user registration' as next_step;
