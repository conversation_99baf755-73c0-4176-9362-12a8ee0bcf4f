-- =====================================================
-- FRESH SUPABASE SETUP FOR NEWS WEBSITE
-- Run this ENTIRE script in Supabase SQL Editor
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- 1. CREATE ENUMS FOR ROLE DROPDOWN
-- =====================================================

-- User roles enum with dropdown support
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('user', 'writer', 'editor', 'admin');
    END IF;
END $$;

-- Article categories enum
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'article_category') THEN
        CREATE TYPE article_category AS ENUM ('Politics', 'Tech', 'World', 'Sports', 'Entertainment', 'Business', 'Health', 'Science');
    END IF;
END $$;

-- Advertisement positions enum
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ad_position') THEN
        CREATE TYPE ad_position AS ENUM ('header', 'sidebar', 'footer', 'inline', 'popup');
    END IF;
END $$;

-- =====================================================
-- 2. CREATE TABLES
-- =====================================================

-- User Profiles Table (linked to auth.users)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    avatar_url TEXT,
    role user_role DEFAULT 'user' NOT NULL,
    bio TEXT,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Articles Table
CREATE TABLE IF NOT EXISTS articles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE,
    summary TEXT NOT NULL,
    content TEXT NOT NULL,
    category article_category DEFAULT 'World',
    image_url TEXT NOT NULL,
    author_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    published_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_featured BOOLEAN DEFAULT false,
    is_breaking BOOLEAN DEFAULT false,
    is_trending BOOLEAN DEFAULT false,
    is_published BOOLEAN DEFAULT true,
    views INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    reading_time INTEGER DEFAULT 5,
    seo_title VARCHAR(255),
    seo_description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Comments Table
CREATE TABLE IF NOT EXISTS comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT true,
    likes INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bookmarks Table
CREATE TABLE IF NOT EXISTS bookmarks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, article_id)
);

-- Newsletter Subscribers Table
CREATE TABLE IF NOT EXISTS newsletter_subscribers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    unsubscribed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Advertisements Table
CREATE TABLE IF NOT EXISTS advertisements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    link_url TEXT NOT NULL,
    position ad_position DEFAULT 'sidebar',
    is_active BOOLEAN DEFAULT true,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    clicks INTEGER DEFAULT 0,
    impressions INTEGER DEFAULT 0,
    budget DECIMAL(10,2),
    created_by UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Website Settings Table
CREATE TABLE IF NOT EXISTS website_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type VARCHAR(20) DEFAULT 'text',
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    updated_by UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. ADD ROLE CONSTRAINT FOR DROPDOWN
-- =====================================================

-- Add constraint for role dropdown
ALTER TABLE user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_role_check;

ALTER TABLE user_profiles 
ADD CONSTRAINT user_profiles_role_check 
CHECK (role IN ('user', 'writer', 'editor', 'admin'));

-- Add comment to help Supabase UI show dropdown
COMMENT ON COLUMN user_profiles.role IS 'User role: user, writer, editor, or admin';

-- =====================================================
-- 4. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- User profiles indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON user_profiles(username);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);

-- Articles indexes
CREATE INDEX IF NOT EXISTS idx_articles_author ON articles(author_id);
CREATE INDEX IF NOT EXISTS idx_articles_published ON articles(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_articles_featured ON articles(is_featured);
CREATE INDEX IF NOT EXISTS idx_articles_category ON articles(category);

-- Comments indexes
CREATE INDEX IF NOT EXISTS idx_comments_article ON comments(article_id);
CREATE INDEX IF NOT EXISTS idx_comments_user ON comments(user_id);

-- Bookmarks indexes
CREATE INDEX IF NOT EXISTS idx_bookmarks_user ON bookmarks(user_id);
CREATE INDEX IF NOT EXISTS idx_bookmarks_article ON bookmarks(article_id);

-- =====================================================
-- 5. CREATE FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON user_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_articles_updated_at 
    BEFORE UPDATE ON articles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_comments_updated_at 
    BEFORE UPDATE ON comments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_newsletter_subscribers_updated_at 
    BEFORE UPDATE ON newsletter_subscribers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_advertisements_updated_at 
    BEFORE UPDATE ON advertisements 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_website_settings_updated_at 
    BEFORE UPDATE ON website_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to increment article views
CREATE OR REPLACE FUNCTION increment_article_views(article_uuid UUID)
RETURNS void AS $$
BEGIN
    UPDATE articles SET views = views + 1 WHERE id = article_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to increment advertisement clicks
CREATE OR REPLACE FUNCTION increment_ad_clicks(ad_uuid UUID)
RETURNS void AS $$
BEGIN
    UPDATE advertisements SET clicks = clicks + 1 WHERE id = ad_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to increment advertisement impressions
CREATE OR REPLACE FUNCTION increment_ad_impressions(ad_uuid UUID)
RETURNS void AS $$
BEGIN
    UPDATE advertisements SET impressions = impressions + 1 WHERE id = ad_uuid;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. USER REGISTRATION AUTO-PROFILE CREATION
-- =====================================================

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    username_val TEXT;
    full_name_val TEXT;
BEGIN
    -- Extract username and full name from metadata or email
    username_val := COALESCE(
        NEW.raw_user_meta_data->>'username',
        split_part(NEW.email, '@', 1)
    );

    full_name_val := COALESCE(
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'name',
        split_part(NEW.email, '@', 1)
    );

    -- Create user profile automatically when user registers
    INSERT INTO public.user_profiles (
        id,
        username,
        full_name,
        email,
        role,
        avatar_url,
        email_verified,
        is_active
    ) VALUES (
        NEW.id,
        username_val,
        full_name_val,
        NEW.email,
        CASE
            WHEN NEW.email = '<EMAIL>' THEN 'admin'
            ELSE 'user'
        END,
        COALESCE(
            NEW.raw_user_meta_data->>'avatar_url',
            'https://api.dicebear.com/7.x/avataaars/svg?seed=' || username_val
        ),
        NEW.email_confirmed_at IS NOT NULL,
        true
    )
    ON CONFLICT (id) DO UPDATE SET
        email = NEW.email,
        email_verified = NEW.email_confirmed_at IS NOT NULL,
        updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- 7. ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookmarks ENABLE ROW LEVEL SECURITY;
ALTER TABLE newsletter_subscribers ENABLE ROW LEVEL SECURITY;
ALTER TABLE advertisements ENABLE ROW LEVEL SECURITY;
ALTER TABLE website_settings ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view all profiles" ON user_profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can manage all profiles" ON user_profiles FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Articles policies
CREATE POLICY "Anyone can view published articles" ON articles FOR SELECT USING (is_published = true);
CREATE POLICY "Authors can view own articles" ON articles FOR SELECT USING (auth.uid() = author_id);
CREATE POLICY "Writers can create articles" ON articles FOR INSERT WITH CHECK (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('writer', 'editor', 'admin'))
);
CREATE POLICY "Authors can update own articles" ON articles FOR UPDATE USING (auth.uid() = author_id);
CREATE POLICY "Editors can manage all articles" ON articles FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('editor', 'admin'))
);

-- Comments policies
CREATE POLICY "Anyone can view approved comments" ON comments FOR SELECT USING (is_approved = true);
CREATE POLICY "Users can create comments" ON comments FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own comments" ON comments FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Moderators can manage comments" ON comments FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('editor', 'admin'))
);

-- Bookmarks policies
CREATE POLICY "Users can manage own bookmarks" ON bookmarks FOR ALL USING (auth.uid() = user_id);

-- Newsletter policies
CREATE POLICY "Anyone can subscribe to newsletter" ON newsletter_subscribers FOR INSERT WITH CHECK (true);
CREATE POLICY "Admins can manage newsletter" ON newsletter_subscribers FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Advertisements policies
CREATE POLICY "Anyone can view active ads" ON advertisements FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can manage ads" ON advertisements FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Website settings policies
CREATE POLICY "Anyone can view public settings" ON website_settings FOR SELECT USING (is_public = true);
CREATE POLICY "Admins can manage settings" ON website_settings FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- =====================================================
-- 8. INSERT INITIAL DATA
-- =====================================================

-- Insert default website settings
INSERT INTO website_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_title', 'News Website', 'text', 'Website title', true),
('site_tagline', 'Your Source for Latest News', 'text', 'Website tagline', true),
('site_description', 'Stay updated with the latest news and stories', 'text', 'Website description for SEO', true),
('articles_per_page', '10', 'number', 'Number of articles per page', false),
('comments_enabled', 'true', 'boolean', 'Enable comments on articles', false),
('moderate_comments', 'true', 'boolean', 'Moderate comments before publishing', false),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', false),
('newsletter_enabled', 'true', 'boolean', 'Enable newsletter subscription', true),
('social_sharing_enabled', 'true', 'boolean', 'Enable social media sharing', true),
('analytics_enabled', 'true', 'boolean', 'Enable analytics tracking', false)
ON CONFLICT (setting_key) DO NOTHING;

-- =====================================================
-- 9. FIX EXISTING USERS (CREATE PROFILES FOR EXISTING AUTH USERS)
-- =====================================================

-- Create profiles for users who already registered before running this script
DO $$
DECLARE
    fixed_count INTEGER := 0;
BEGIN
    INSERT INTO public.user_profiles (
        id,
        username,
        full_name,
        email,
        role,
        avatar_url,
        email_verified,
        is_active
    )
    SELECT
        au.id,
        COALESCE(au.raw_user_meta_data->>'username', split_part(au.email, '@', 1)),
        COALESCE(au.raw_user_meta_data->>'full_name', au.raw_user_meta_data->>'name', split_part(au.email, '@', 1)),
        au.email,
        CASE
            WHEN au.email = '<EMAIL>' THEN 'admin'
            ELSE 'user'
        END,
        COALESCE(au.raw_user_meta_data->>'avatar_url', 'https://api.dicebear.com/7.x/avataaars/svg?seed=' || split_part(au.email, '@', 1)),
        au.email_confirmed_at IS NOT NULL,
        true
    FROM auth.users au
    LEFT JOIN public.user_profiles up ON au.id = up.id
    WHERE up.id IS NULL;

    GET DIAGNOSTICS fixed_count = ROW_COUNT;
    RAISE NOTICE '✅ Fixed % existing users without profiles', fixed_count;
END $$;

-- =====================================================
-- 10. VERIFICATION AND FINAL SETUP
-- =====================================================

-- Verification queries
SELECT 'SETUP VERIFICATION:' as status;

-- Check tables exist
SELECT 'TABLES CHECK:' as check_type;
SELECT
    table_name,
    '✅ Created' as status
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('user_profiles', 'articles', 'comments', 'bookmarks', 'newsletter_subscribers', 'advertisements', 'website_settings')
ORDER BY table_name;

-- Check role constraint
SELECT 'ROLE CONSTRAINT CHECK:' as check_type;
SELECT
    constraint_name,
    '✅ Role dropdown enabled' as status
FROM information_schema.table_constraints
WHERE constraint_name = 'user_profiles_role_check';

-- Check admin users
SELECT 'ADMIN USERS CHECK:' as check_type;
SELECT
    username,
    full_name,
    email,
    role,
    '✅ Admin ready' as status
FROM user_profiles
WHERE role = 'admin'
ORDER BY created_at;

-- Check triggers
SELECT 'TRIGGERS CHECK:' as check_type;
SELECT
    trigger_name,
    event_object_table,
    '✅ Active' as status
FROM information_schema.triggers
WHERE trigger_name IN ('on_auth_user_created', 'update_user_profiles_updated_at')
ORDER BY trigger_name;

-- Final success message
SELECT '
🎉 SETUP SUCCESSFUL!

✅ WHAT WAS CREATED:
• All database tables with proper relationships
• Role dropdown constraint (user, writer, editor, admin)
• User registration auto-profile creation
• Row Level Security policies
• Performance indexes and triggers
• Default website settings

👤 ADMIN SETUP:
📧 Your Email: <EMAIL>
🎯 Role: Will be automatically set to admin when you register
📝 Note: No demo users created - clean setup

🔧 HOW TO USE:

1. REGISTER YOUR ADMIN ACCOUNT:
   - Go to your website registration page
   - Register with: <EMAIL>
   - Will automatically get admin role
   - Full admin access granted

2. ROLE DROPDOWN IN SUPABASE:
   - Go to Supabase → Table Editor → user_profiles
   - Role column shows dropdown with options:
     * user (default for new registrations)
     * writer (can create articles)
     * editor (can manage articles and comments)
     * admin (full access)

3. MANUAL ROLE CHANGES:
   - After users register, go to user_profiles table
   - Click on any user role field
   - Select new role from dropdown
   - Changes take effect immediately

4. USER REGISTRATION FLOW:
   - User registers → Goes to auth.users
   - Trigger automatically creates profile in user_profiles
   - Default role: user
   - You manually change role via Supabase interface

🎯 NEXT STEPS:
1. Register your admin account
2. Test user registration flow
3. Verify role dropdown works in Supabase
4. Manually promote test users to different roles

Ready to use! 🚀
' as final_instructions;
