-- =====================================================
-- FIX THE TRIGGER FUNCTION TO PREVENT 500 ERRORS
-- Run this in Supabase SQL Editor
-- =====================================================

-- Drop the existing trigger and function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Create a simpler, more robust trigger function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    username_val TEXT;
    full_name_val TEXT;
    username_counter INTEGER := 0;
    final_username TEXT;
BEGIN
    -- Extract username and full name from metadata or email
    username_val := COALESCE(
        NEW.raw_user_meta_data->>'username',
        split_part(NEW.email, '@', 1)
    );

    full_name_val := COALESCE(
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'name',
        split_part(NEW.email, '@', 1)
    );

    -- Ensure username is unique by adding a counter if needed
    final_username := username_val;
    WHILE EXISTS (SELECT 1 FROM public.user_profiles WHERE username = final_username) LOOP
        username_counter := username_counter + 1;
        final_username := username_val || username_counter::text;
    END LOOP;

    -- Create user profile automatically when user registers
    INSERT INTO public.user_profiles (
        id,
        username,
        full_name,
        email,
        role,
        avatar_url,
        email_verified,
        is_active
    ) VALUES (
        NEW.id,
        final_username,
        full_name_val,
        NEW.email,
        CASE
            WHEN NEW.email = '<EMAIL>' THEN 'admin'::user_role
            ELSE 'user'::user_role
        END,
        COALESCE(
            NEW.raw_user_meta_data->>'avatar_url',
            'https://api.dicebear.com/7.x/avataaars/svg?seed=' || final_username
        ),
        COALESCE(NEW.email_confirmed_at IS NOT NULL, false),
        true
    );

    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't fail the user creation
        RAISE WARNING 'Failed to create user profile for %: %', NEW.email, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Test the function
SELECT 'Trigger function updated successfully!' as status;
SELECT 'You can now test user registration' as next_step;
