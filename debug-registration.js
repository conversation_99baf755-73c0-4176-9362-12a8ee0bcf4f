// Simple debug script to test registration without external dependencies
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://dpqinppbfevdezibxzrq.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwcWlucHBiZmV2ZGV6aWJ4enJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNjg4NDEsImV4cCI6MjA2Nzc0NDg0MX0.gtwVDAb3btl4msxkSMwhNw1UPKVY3HZYetNZNN1UNCg';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugRegistration() {
  console.log('🔍 Debugging registration issue...\n');

  try {
    // 1. Test database connection
    console.log('1. Testing database connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.log('❌ Database connection failed:', connectionError.message);
      return;
    }
    console.log('✅ Database connection successful');

    // 2. Check if tables exist
    console.log('\n2. Checking if tables exist...');
    const { data: tables, error: tablesError } = await supabase
      .from('user_profiles')
      .select('id')
      .limit(1);

    if (tablesError) {
      console.log('❌ user_profiles table issue:', tablesError.message);
      console.log('🔧 Make sure you ran the supabase-setup.sql script');
      return;
    }
    console.log('✅ user_profiles table exists');

    // 3. Check if trigger exists
    console.log('\n3. Checking database triggers...');
    const { data: triggers, error: triggerError } = await supabase
      .rpc('exec_sql', { 
        sql: `SELECT trigger_name FROM information_schema.triggers WHERE trigger_name = 'on_auth_user_created';` 
      });

    if (triggerError) {
      console.log('⚠️  Cannot check triggers (this is normal)');
    } else {
      console.log('✅ Trigger check completed');
    }

    // 4. Test registration with a unique email
    console.log('\n4. Testing user registration...');
    const testEmail = `test-${Date.now()}@example.com`;
    const testPassword = 'testpassword123';
    const testUsername = `testuser${Date.now()}`;
    const testFullName = 'Test User';

    console.log(`Registering: ${testEmail}`);
    
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          username: testUsername,
          full_name: testFullName,
          avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${testUsername}`,
        },
      },
    });

    if (authError) {
      console.log('❌ Registration failed:', authError.message);
      console.log('Error details:', authError);
      return;
    }

    console.log('✅ User registered in auth.users');
    console.log('User ID:', authData.user?.id);

    // 5. Wait and check if profile was created
    console.log('\n5. Waiting for profile creation...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', authData.user?.id)
      .single();

    if (profileError) {
      console.log('❌ Profile creation failed:', profileError.message);
      console.log('🔧 This indicates the trigger is not working properly');
      console.log('🔧 Make sure you ran the complete supabase-setup.sql script');
      
      // Try to create profile manually to test table structure
      console.log('\n6. Testing manual profile creation...');
      const { data: manualProfile, error: manualError } = await supabase
        .from('user_profiles')
        .insert({
          id: authData.user?.id,
          username: testUsername + '_manual',
          full_name: testFullName,
          email: testEmail,
          role: 'user',
          avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${testUsername}`,
          email_verified: false,
          is_active: true
        })
        .select()
        .single();

      if (manualError) {
        console.log('❌ Manual profile creation failed:', manualError.message);
        console.log('🔧 This indicates a table structure issue');
      } else {
        console.log('✅ Manual profile creation successful');
        console.log('Profile:', manualProfile);
      }
    } else {
      console.log('✅ Profile created automatically by trigger!');
      console.log('Profile data:', profile);
    }

    console.log('\n📊 Debug completed!');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error('Full error:', error);
  }
}

// Run the debug
debugRegistration();
