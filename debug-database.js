// Debug database state and test manual profile creation
import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugDatabase() {
  console.log('🔍 Debugging database state...\n');

  try {
    // 1. Test manual profile creation
    console.log('1. Testing manual profile creation...');
    const testId = randomUUID();
    const testUsername = `testuser${Date.now()}`;
    const testEmail = `test-${Date.now()}@example.com`;

    const { data: manualProfile, error: manualError } = await supabase
      .from('user_profiles')
      .insert({
        id: testId,
        username: testUsername,
        full_name: 'Test User',
        email: testEmail,
        role: 'user',
        avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${testUsername}`,
        email_verified: false,
        is_active: true
      })
      .select()
      .single();

    if (manualError) {
      console.log('❌ Manual profile creation failed:', manualError.message);
      console.log('Error details:', JSON.stringify(manualError, null, 2));
    } else {
      console.log('✅ Manual profile creation successful!');
      console.log('Profile:', manualProfile);
      
      // Clean up
      await supabase.from('user_profiles').delete().eq('id', testId);
      console.log('🧹 Test profile cleaned up');
    }

    // 2. Check table structure
    console.log('\n2. Checking table structure...');
    const { data: tableInfo, error: tableError } = await supabase
      .from('user_profiles')
      .select('*')
      .limit(1);

    if (tableError) {
      console.log('❌ Table structure issue:', tableError.message);
    } else {
      console.log('✅ Table structure looks good');
      if (tableInfo && tableInfo.length > 0) {
        console.log('Sample row structure:', Object.keys(tableInfo[0]));
      }
    }

    // 3. Test auth user creation without trigger
    console.log('\n3. Testing auth user creation (no profile trigger)...');
    const testAuthEmail = `auth-test-${Date.now()}@example.com`;
    
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: testAuthEmail,
      password: 'testpassword123',
      options: {
        data: {
          username: `authtest${Date.now()}`,
          full_name: 'Auth Test User',
        },
      },
    });

    if (authError) {
      console.log('❌ Auth user creation failed:', authError.message);
      console.log('Error details:', JSON.stringify(authError, null, 2));
    } else {
      console.log('✅ Auth user created successfully!');
      console.log('User ID:', authData.user?.id);
      
      // Check if profile was created by trigger
      setTimeout(async () => {
        const { data: triggerProfile, error: triggerError } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', authData.user?.id)
          .single();

        if (triggerError) {
          console.log('❌ Trigger did not create profile:', triggerError.message);
          console.log('🔧 The trigger function might not be working');
        } else {
          console.log('✅ Trigger created profile successfully!');
          console.log('Profile:', triggerProfile);
        }
      }, 2000);
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error('Full error:', error);
  }
}

debugDatabase();
