-- =====================================================
-- COMPLETE RESET AND SETUP
-- Run this ENTIRE script in Supabase SQL Editor
-- =====================================================

-- 1. Disable RLS temporarily for setup
ALTER TABLE IF EXISTS user_profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS articles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS bookmarks DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS newsletter_subscribers DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS advertisements DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS website_settings DISABLE ROW LEVEL SECURITY;

-- 2. Drop existing trigger and function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- 3. Ensure user_profiles table has correct structure
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    avatar_url TEXT,
    role user_role DEFAULT 'user' NOT NULL,
    bio TEXT,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create the working trigger function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    username_val TEXT;
    full_name_val TEXT;
    final_username TEXT;
BEGIN
    -- Extract username and full name from metadata or email
    username_val := COALESCE(
        NEW.raw_user_meta_data->>'username',
        split_part(NEW.email, '@', 1)
    );

    full_name_val := COALESCE(
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'name',
        split_part(NEW.email, '@', 1)
    );

    -- Make username unique by appending timestamp if needed
    final_username := username_val || '_' || extract(epoch from now())::bigint::text;

    -- Create user profile
    INSERT INTO public.user_profiles (
        id,
        username,
        full_name,
        email,
        role,
        avatar_url,
        email_verified,
        is_active
    ) VALUES (
        NEW.id,
        final_username,
        full_name_val,
        NEW.email,
        CASE
            WHEN NEW.email = '<EMAIL>' THEN 'admin'::user_role
            ELSE 'user'::user_role
        END,
        COALESCE(
            NEW.raw_user_meta_data->>'avatar_url',
            'https://api.dicebear.com/7.x/avataaars/svg?seed=' || final_username
        ),
        COALESCE(NEW.email_confirmed_at IS NOT NULL, false),
        true
    );

    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Don't fail user creation if profile creation fails
        RAISE WARNING 'Profile creation failed for %: %', NEW.email, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 6. Re-enable RLS with simple policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Simple policies that won't cause recursion
CREATE POLICY "Allow all operations for authenticated users" ON user_profiles
    FOR ALL USING (auth.uid() IS NOT NULL);

CREATE POLICY "Allow public read access" ON user_profiles
    FOR SELECT USING (true);

-- 7. Verification
SELECT 'SETUP COMPLETE!' as status;
SELECT 'Trigger function created and enabled' as trigger_status;
SELECT 'RLS enabled with simple policies' as rls_status;
SELECT 'Ready to test registration!' as next_step;
