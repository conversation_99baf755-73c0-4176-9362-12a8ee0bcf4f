// Test script to verify registration functionality
// Run this with: node test-registration.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDatabaseSetup() {
  console.log('🔍 Testing database setup...\n');

  try {
    // Test 1: Check if trigger function exists
    console.log('1. Checking if trigger function exists...');
    const { data: functions, error: funcError } = await supabase
      .rpc('sql', { 
        query: "SELECT proname FROM pg_proc WHERE proname = 'handle_new_user';" 
      });
    
    if (funcError) {
      console.log('❌ Could not check trigger function (this is expected if R<PERSON> is not enabled)');
    } else {
      console.log('✅ Trigger function check completed');
    }

    // Test 2: Check if user_profiles table exists and has correct structure
    console.log('\n2. Checking user_profiles table structure...');
    const { data: profiles, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .limit(1);

    if (profileError) {
      console.log('❌ Error accessing user_profiles table:', profileError.message);
    } else {
      console.log('✅ user_profiles table is accessible');
    }

    // Test 3: Check RLS policies
    console.log('\n3. Checking Row Level Security...');
    const { data: rlsData, error: rlsError } = await supabase
      .from('user_profiles')
      .select('id')
      .limit(1);

    if (rlsError) {
      console.log('❌ RLS might be blocking access:', rlsError.message);
    } else {
      console.log('✅ RLS policies are working correctly');
    }

    console.log('\n📊 Database setup test completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Run the database-updates.sql in your Supabase SQL Editor');
    console.log('2. Test registration through the web interface');
    console.log('3. Check if profiles are created automatically');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function testRegistration() {
  console.log('\n🧪 Testing registration flow...\n');
  
  const testEmail = `test-${Date.now()}@example.com`;
  const testPassword = 'testpassword123';
  const testUsername = `testuser${Date.now()}`;
  const testFullName = 'Test User';

  try {
    console.log('1. Attempting to register user...');
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          username: testUsername,
          full_name: testFullName,
          avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${testUsername}`,
        },
      },
    });

    if (error) {
      console.log('❌ Registration failed:', error.message);
      console.log('Error details:', JSON.stringify(error, null, 2));
      return;
    }

    console.log('✅ User registered successfully');
    console.log('User ID:', data.user?.id);

    // Wait a moment for the trigger to execute
    console.log('\n2. Waiting for profile creation...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check if profile was created
    console.log('3. Checking if profile was created...');
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', data.user?.id)
      .single();

    if (profileError) {
      console.log('❌ Profile not found:', profileError.message);
      console.log('🔧 This means the trigger is not working properly');
    } else {
      console.log('✅ Profile created successfully!');
      console.log('Profile data:', profile);
    }

    // Clean up - delete the test user profile
    if (profile) {
      await supabase
        .from('user_profiles')
        .delete()
        .eq('id', data.user?.id);
      console.log('🧹 Test profile cleaned up');
    }

  } catch (error) {
    console.error('❌ Registration test failed:', error.message);
  }
}

// Run tests
async function runTests() {
  await testDatabaseSetup();
  await testRegistration();
}

runTests().catch(console.error);
